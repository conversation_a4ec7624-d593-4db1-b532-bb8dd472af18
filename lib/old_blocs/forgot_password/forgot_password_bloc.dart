import 'package:amazon_cognito_identity_dart_2/cognito.dart';
import 'package:connectone/old_blocs/forgot_password/forgot_password_state.dart';
import 'package:connectone/old_screens/login_screen.dart';
import 'package:connectone/core/utils/safe_print.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';

import '../../core/network/network_controller.dart';
import '../../core/utils/constants.dart';

class ForgotPasswordBloc extends Cubit<ForgotPasswordState> {
  ForgotPasswordBloc(initialState) : super(initialState);
  final _networkController = NetworkController();
  FirebaseAuth auth = FirebaseAuth.instance;
  String? vCognitoToken;

  willPopClick() {
    var newState = state.copyWith(
        confirmationCode: !state.confirmationCode,
        emailEditable: !state.emailEditable);
    emit(newState);
  }

  /// Validates mobile number and checks if it exists in Cognito user pool
  /// Returns true if mobile number exists, false otherwise
  Future<bool> validateMobileNumberExists(String mobileNumber) async {
    // Validate mobile number format first
    if (mobileNumber.isEmpty) {
      alert("Please enter a mobile number");
      return false;
    }

    // Remove any spaces or special characters except +
    String cleanedNumber = mobileNumber.replaceAll(RegExp(r'[^\d+]'), '');

    // Validate mobile number format (10 digits for Indian numbers)
    if (!RegExp(r'^[0-9]{10}$').hasMatch(cleanedNumber)) {
      alert("Please enter a valid 10-digit mobile number");
      return false;
    }

    // Format the mobile number for Cognito (add +91 prefix)
    String formattedNumber = "+91$cleanedNumber";

    // Show loading state
    var loadingState = state.copyWith(isLoading: true);
    emit(loadingState);

    final userPool = CognitoUserPool(
      _networkController.organisationData?.userPoolId ?? "",
      _networkController.organisationData?.appClientWeb ?? "",
    );
    final cognitoUser = CognitoUser(formattedNumber, userPool);

    try {
      // Try to initiate forgot password to check if user exists
      await cognitoUser.forgotPassword();

      // If we reach here, the user exists and forgot password was initiated
      var successState = state.copyWith(
          isLoading: false, confirmationCode: true, emailEditable: false);
      emit(successState);

      alert("Verification code has been sent to $formattedNumber");
      return true;

    } catch (e) {
      safePrint("Mobile validation error: $e");
      var errorState = state.copyWith(isLoading: false);
      emit(errorState);

      if (e is CognitoClientException) {
        // Check for specific error types related to user not found
        if (e.message?.contains("UserNotFoundException") == true ||
            e.message?.contains("User does not exist") == true ||
            e.message?.contains("Username/client id combination not found") == true) {
          alert("Mobile number not found. Please check and try again.");
          return false;
        } else {
          alert(e.message ?? "Something went wrong. Please try again.");
          return false;
        }
      } else {
        // Check if the error string contains user not found indicators
        String errorString = e.toString().toLowerCase();
        if (errorString.contains("usernotfoundexception") ||
            errorString.contains("user does not exist") ||
            errorString.contains("user not found")) {
          alert("Mobile number not found. Please check and try again.");
          return false;
        } else {
          alert("Something went wrong. Please try again.");
          return false;
        }
      }
    }
  }

  forgotPassword(String username) async {
    if (username.isEmpty) {
      alert(sEnterAValidEmail);
      return;
    }
    if (!username.contains("@")) {
      username = "+91$username";
    }
    var newState = state.copyWith(isLoading: true);
    emit(newState);
    final userPool = CognitoUserPool(
      _networkController.organisationData?.userPoolId ?? "",
      _networkController.organisationData?.appClientWeb ?? "",
    );
    final cognitoUser = CognitoUser(username, userPool);
    try {
      var data = await cognitoUser.forgotPassword();
      alert("$sCodeIsSendTo $username");
      safePrint(data);
      var newState = state.copyWith(
          isLoading: false, confirmationCode: true, emailEditable: false);
      emit(newState);
    } catch (e) {
      safePrint(e);
      var newState = state.copyWith(isLoading: false);
      emit(newState);
      if (e is CognitoClientException) {
        // Check for specific error types related to user not found
        if (e.message?.contains("UserNotFoundException") == true ||
            e.message?.contains("User does not exist") == true ||
            e.message?.contains("Username/client id combination not found") == true) {
          alert(sPhoneNotRegistered);
        } else {
          alert(e.message ?? sSomethingWentWrong);
        }
      } else {
        // Check if the error string contains user not found indicators
        String errorString = e.toString().toLowerCase();
        if (errorString.contains("usernotfoundexception") ||
            errorString.contains("user does not exist") ||
            errorString.contains("user not found")) {
          alert(sPhoneNotRegistered);
        } else {
          alert(sSomethingWentWrong);
        }
      }
    }
  }

  Future<bool> confirmPassword(String username, String code, String newPassword,
      String confirmPassword) async {
    final userPool = CognitoUserPool(
      _networkController.organisationData?.userPoolId ?? "",
      _networkController.organisationData?.appClientWeb ?? "",
    );
    if (isPhoneNumber) {
      username = "+91$username";
    }
    safePrint("pppp $username $code $newPassword $confirmPassword");
    final cognitoUser = CognitoUser(username, userPool);
    try {
      if (code.isEmpty) {
        alert(sEnterAValidCode);
        return false;
      }
      if (newPassword.isEmpty || newPassword != confirmPassword) {
        alert("Please enter correct passwords!");
        return false;
      }
      var newState = state.copyWith(isLoading: true);
      emit(newState);
      var passwordConfirmed =
          await cognitoUser.confirmPassword(code, newPassword);

      var newState1 = state.copyWith(isLoading: false);
      emit(newState1);
      alert(sPasswordChangedSuccessfully);
      Get.off(() => const LoginScreen());
      return passwordConfirmed;
    } catch (e) {
      safePrint("qqqq $e");
      var newState = state.copyWith(isLoading: false);
      emit(newState);
      if (e is CognitoClientException) {
        alert(e.message);
      } else {
        alert(sPasswordChangeFailed);
      }
      return false;
    }
  }
}
